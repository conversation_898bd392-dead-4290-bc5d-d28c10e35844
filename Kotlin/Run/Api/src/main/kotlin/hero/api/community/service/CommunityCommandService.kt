package hero.api.community.service

import hero.exceptions.http.ConflictException
import hero.exceptions.http.ForbiddenException
import hero.model.Community
import hero.repository.community.CommunityRepository
import hero.repository.user.UserRepository
import java.time.Clock
import java.time.Instant
import java.util.UUID

class CommunityCommandService(
    private val communityRepository: CommunityRepository,
    private val userRepository: UserRepository,
    private val clock: Clock = Clock.systemUTC(),
) {
    fun execute(command: CreateCommunity) {
        val creator = userRepository.getById(command.userId)

        if (!creator.canCreateCommunity) {
            throw ForbiddenException("User ${command.userId} cannot create communities")
        }

        if (communityRepository.findByOwnerId(command.userId).isNotEmpty()) {
            throw ConflictException("User ${command.userId} already has a community")
        }

        val now = Instant.now(clock)
        val community = Community(
            id = UUID.randomUUID(),
            name = creator.name,
            description = "Community",
            slug = creator.path,
            ownerId = command.userId,
            membersCount = 1,
            image = creator.image,
            createdAt = now,
            updatedAt = now,
            deletedAt = null,
        )
    }
}

data class CreateCommunity(val userId: String)
